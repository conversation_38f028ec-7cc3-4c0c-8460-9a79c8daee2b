<?php

namespace Database\Seeders;

use Botble\Base\Supports\BaseSeeder;
use Botble\Language\Models\LanguageMeta;
use Botble\Testimonial\Models\Testimonial;

class TestimonialSeeder extends BaseSeeder
{
    public function run(): void
    {
        $this->uploadFiles('testimonials');

        $data = [
            [
                'name' => '<PERSON>',
                'company' => 'CEO Of Microsoft',
            ],
            [
                'name' => '<PERSON><PERSON>',
                'company' => 'CEO Of Apple',
            ],
            [
                'name' => '<PERSON>',
                'company' => 'Pio Founder',
            ],
        ];

        Testimonial::query()->truncate();

        foreach ($data as $index => $item) {
            $item['image'] = 'testimonials/0' . ($index + 1) . '.png';
            $item['content'] = 'Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua';

            $testimonial = Testimonial::query()->create($item);

            LanguageMeta::saveMetaData($testimonial);
        }
    }
}
