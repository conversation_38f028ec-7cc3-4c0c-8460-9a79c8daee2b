<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ht_bookings', function (Blueprint $table) {
            if (!Schema::hasColumn('ht_bookings', 'meta_data')) {
                $table->json('meta_data')->nullable();
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ht_bookings', function (Blueprint $table) {
            if (Schema::hasColumn('ht_bookings', 'meta_data')) {
                $table->dropColumn('meta_data');
            }
        });
    }
};
