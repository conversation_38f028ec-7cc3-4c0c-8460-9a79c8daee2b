<?php

namespace Botble\Hotel\Models;

use Botble\Base\Enums\BaseStatusEnum;
use Botble\Base\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

class Room extends BaseModel
{
    protected $table = 'ht_rooms';

    protected $fillable = [
        'name',
        'description',
        'content',
        'is_featured',
        'images',
        'price',
        'currency_id',
        'number_of_rooms',
        'number_of_beds',
        'size',
        'max_adults',
        'max_children',
        'room_category_id',
        'tax_id',
        'order',
        'status',
    ];

    protected $casts = [
        'status' => BaseStatusEnum::class,
    ];

    public function getImagesAttribute($value)
    {
        if ($value === '[null]') {
            return [];
        }

        $images = json_decode((string) $value, true);

        if (is_array($images)) {
            $images = array_filter($images);
        }

        return $images ?: [];
    }

    public function getImageAttribute(): ?string
    {
        return Arr::first($this->images) ?? null;
    }

    public function amenities(): BelongsToMany
    {
        return $this->belongsToMany(Amenity::class, 'ht_rooms_amenities', 'room_id', 'amenity_id');
    }

    public function currency(): BelongsTo
    {
        return $this->belongsTo(Currency::class, 'currency_id')->withDefault();
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(RoomCategory::class, 'room_category_id')->withDefault();
    }

    public function isAvailableAt(array $filters): bool
    {
        try {
            $startDate = $filters['start_date'];
            $endDate = $filters['end_date'];
            $requestedRooms = $filters['rooms'] ?? 1;

            \Log::info('Room: Checking availability', [
                'room_id' => $this->id,
                'room_name' => $this->name,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'requested_rooms' => $requestedRooms
            ]);

            // First check if we have inventory items
            $inventory = $this->inventory;
            if ($inventory && $inventory->isNotEmpty()) {
                \Log::info('Room: Using inventory-based availability check', [
                    'room_id' => $this->id,
                    'inventory_count' => $inventory->count()
                ]);

                $availableInventoryCount = 0;
                foreach ($inventory as $inventoryItem) {
                    if ($inventoryItem->isAvailableForDateRange($startDate, $endDate)) {
                        $availableInventoryCount++;
                    }
                }

                $isAvailable = $availableInventoryCount >= $requestedRooms;

                \Log::info('Room: Inventory availability result', [
                    'room_id' => $this->id,
                    'available_inventory' => $availableInventoryCount,
                    'requested_rooms' => $requestedRooms,
                    'is_available' => $isAvailable
                ]);

                return $isAvailable;
            }

            \Log::info('Room: Using legacy availability check', [
                'room_id' => $this->id,
                'number_of_rooms' => $this->number_of_rooms
            ]);

            // Fall back to the original room availability logic if no inventory exists
            $allDates = [];

            // Build date range array with default room counts
            for (
                $index = strtotime($startDate);
                $index < strtotime($endDate);
                $index += 60 * 60 * 24
            ) {
                $allDates[date('Y-m-d', $index)] = [
                    'number' => $this->number_of_rooms,
                    'price' => $this->price,
                ];
            }

            // Apply room date overrides if any exist
            $roomDates = $this->activeRoomDates;
            if ($roomDates->isNotEmpty()) {
                foreach ($roomDates as $row) {
                    $dateKey = date('Y-m-d', strtotime($row->start_date));
                    if (!array_key_exists($dateKey, $allDates)) {
                        continue;
                    }

                    // If room date is not active or has no rooms/value, mark as unavailable
                    if (!$row->active || !$row->number_of_rooms || !$row->value) {
                        \Log::info('Room: Date marked as unavailable due to room date settings', [
                            'room_id' => $this->id,
                            'date' => $dateKey,
                            'active' => $row->active,
                            'number_of_rooms' => $row->number_of_rooms,
                            'value' => $row->value
                        ]);
                        return false;
                    }

                    $allDates[$dateKey] = [
                        'number' => $row->number_of_rooms,
                        'price' => $row->value,
                    ];
                }
            }

            // Subtract existing bookings from available room counts
            $roomBookings = $this->activeBookingRooms;
            if ($roomBookings->isNotEmpty()) {
                foreach ($roomBookings as $roomBooking) {
                    for (
                        $index = strtotime($roomBooking->start_date);
                        $index < strtotime($roomBooking->end_date);
                        $index += 60 * 60 * 24
                    ) {
                        $dateKey = date('Y-m-d', $index);
                        if (!array_key_exists($dateKey, $allDates)) {
                            continue;
                        }

                        $allDates[$dateKey]['number'] -= $roomBooking->number_of_rooms;

                        if ($allDates[$dateKey]['number'] <= 0) {
                            \Log::info('Room: Date unavailable due to existing bookings', [
                                'room_id' => $this->id,
                                'date' => $dateKey,
                                'remaining_rooms' => $allDates[$dateKey]['number']
                            ]);
                            return false;
                        }
                    }
                }
            }

            // Get the minimum available rooms across all dates in the range
            $availableRoomCounts = array_column($allDates, 'number');
            $maxNumberPerDay = 0;
            if ($availableRoomCounts) {
                $maxNumberPerDay = (int) min($availableRoomCounts);
            }

            $isAvailable = $maxNumberPerDay >= $requestedRooms;

            \Log::info('Room: Legacy availability result', [
                'room_id' => $this->id,
                'max_rooms_per_day' => $maxNumberPerDay,
                'requested_rooms' => $requestedRooms,
                'is_available' => $isAvailable
            ]);

            return $isAvailable;

        } catch (\Exception $e) {
            \Log::error('Room: Error checking availability', [
                'room_id' => $this->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // If there's an error, return false to be safe
            return false;
        }
    }

    public function getRoomTotalPrice(string $startDate, string $endDate, ?int $rooms = 1): float|int
    {
        $rooms = $rooms ?: 1;

        $startDate = Carbon::parse($startDate);
        $endDate = Carbon::parse($endDate);
        $nights = $startDate->diffInDays($endDate);

        $roomDates = $this->activeRoomDates;
        $originalPrice = $this->price * $nights * $rooms;

        if ($roomDates->isEmpty()) {
            return $originalPrice;
        }

        $allDates = [];
        for ($index = strtotime($startDate); $index < strtotime($endDate); $index += 60 * 60 * 24) {
            $allDates[date('Y-m-d', $index)] = [
                'number' => $this->number_of_rooms,
                'price' => $this->price,
            ];
        }

        if (! empty($roomDates)) {
            $price = 0;
            $specialPriceCount = 0;
            foreach ($roomDates as $row) {
                if (! $row->active || ! $row->number_of_rooms || ! $row->value) {
                    continue;
                }

                if (! array_key_exists(date('Y-m-d', strtotime($row->start_date)), $allDates)) {
                    continue;
                }

                if ($row->value_type === 'fixed') {
                    $newPrice = $row->value;
                } elseif ($row->value_type === 'amount_adjust') {
                    $newPrice = $this->price + $row->value;
                } else {
                    $newPrice = $this->price + $this->price * $row->value / 100;
                }

                $price += $newPrice;
                $specialPriceCount++;
            }

            return $price + ($nights - $specialPriceCount) * $this->price * $rooms;
        }

        return $originalPrice;
    }

    public function activeRoomDates(): HasMany
    {
        return $this->hasMany(RoomDate::class, 'room_id');
    }

    public function prices(): HasMany
    {
        return $this->hasMany(RoomPrice::class, 'room_id');
    }

    public function activePrices(): HasMany
    {
        return $this->hasMany(RoomPrice::class, 'room_id')->where('is_active', true);
    }


    public function activeBookingRooms(): HasMany
    {
        return $this
            ->hasMany(BookingRoom::class, 'room_id')
            ->active();
    }

    public function tax(): BelongsTo
    {
        return $this->belongsTo(Tax::class, 'tax_id')->withDefault();
    }

    public function reviews(): HasMany
    {
        return $this->hasMany(Review::class, 'room_id');
    }

    public function inventory(): HasMany
    {
        return $this->hasMany(RoomInventory::class, 'room_id');
    }

    /**
     * Get available room inventory items for a date range
     */
    public function getAvailableRoomInventory($startDate, $endDate): array
    {
        // Check if the inventory table exists
        try {
            $inventoryItems = $this->inventory()->where('is_available', true)->get();
            $availableRooms = [];

            foreach ($inventoryItems as $item) {
                if ($item->isAvailableForDateRange($startDate, $endDate)) {
                    $availableRooms[] = $item;
                }
            }

            return $availableRooms;
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return an empty array
            return [];
        }
    }

    /**
     * Check if a specific room ID is available for a date range
     */
    public function isRoomIdAvailable($roomIdCode, $startDate, $endDate): bool
    {
        try {
            $inventoryItem = $this->inventory()->where('room_id_code', $roomIdCode)->first();

            if (!$inventoryItem) {
                return false;
            }

            // Instead of just checking is_available flag, check for date-specific availability
            return $inventoryItem->isAvailableForDateRange($startDate, $endDate);
        } catch (\Exception $e) {
            // If there's an error (like table doesn't exist), return true to avoid blocking bookings
            Log::error('Error checking room availability: ' . $e->getMessage());
            return true;
        }
    }

    /**
     * Get the count of available rooms for a date range
     */
    public function getAvailableRoomCount($startDate, $endDate): int
    {
        try {
            return count($this->getAvailableRoomInventory($startDate, $endDate));
        } catch (\Exception $e) {
            // If there's an error, return the default number of rooms
            return $this->number_of_rooms;
        }
    }

    /**
     * Check if the combination of adults and children is valid for a room type
     * This implements flexible validation for standard rooms and other room types
     */
    public function isValidCombination(string $roomType, int $adults, int $children): bool
    {
        // Log the validation attempt for debugging
        \Illuminate\Support\Facades\Log::info('Room model checking combination validity', [
            'roomType' => $roomType,
            'adults' => $adults,
            'children' => $children
        ]);

        // Special case: Standard Room with 2 adults and 2 children is not allowed
        if (stripos($roomType, 'standard') !== false && $adults === 2 && $children === 2) {
            \Illuminate\Support\Facades\Log::info('Room model: REJECTED - Standard Room with 2 adults and 2 children', [
                'roomType' => $roomType
            ]);
            return false;
        }

        // For other room types, use the flexible configuration
        $roomConfigurations = [
            'Standard Room' => [
                'max_total' => 3,
                'allowed_combinations' => [
                    ['adults' => 1, 'children' => 2], // 1 adult, 2 children
                    ['adults' => 2, 'children' => 1], // 2 adults, 1 child
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child
                    ['adults' => 1, 'children' => 0], // 1 adult, no children
                    ['adults' => 2, 'children' => 0], // 2 adults, no children
                    ['adults' => 3, 'children' => 0], // 3 adults, no children
                    // Note: 2 adults, 2 children is explicitly excluded
                ]
            ],
            'Double Bed Room' => [
                'max_total' => 4,
                'allowed_combinations' => [
                    ['adults' => 3, 'children' => 1], // 3 adults, 1 child
                    ['adults' => 3, 'children' => 0], // 3 adults, no children
                    ['adults' => 2, 'children' => 2], // 2 adults, 2 children
                    ['adults' => 2, 'children' => 1], // 2 adults, 1 child
                    ['adults' => 2, 'children' => 0], // 2 adults, no children
                    ['adults' => 1, 'children' => 3], // 1 adult, 3 children
                    ['adults' => 1, 'children' => 2], // 1 adult, 2 children
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child
                    ['adults' => 1, 'children' => 0], // 1 adult, no children
                    // Note: 4 adults is explicitly excluded
                ]
            ],
            'Luxury Room' => [
                'max_total' => 2,
                'allowed_combinations' => [
                    ['adults' => 2, 'children' => 0], // Only couples allowed
                ]
            ],
            '2BHK' => [
                'max_total' => 7,
                'allowed_combinations' => [
                    ['adults' => 7, 'children' => 0], // 7 adults
                    ['adults' => 6, 'children' => 1], // 6 adults, 1 child
                    ['adults' => 5, 'children' => 2], // 5 adults, 2 children
                    ['adults' => 4, 'children' => 3], // 4 adults, 3 children
                    ['adults' => 3, 'children' => 4], // 3 adults, 4 children
                    ['adults' => 2, 'children' => 5], // 2 adults, 5 children
                    ['adults' => 1, 'children' => 6], // 1 adult, 6 children
                    // Common family combinations
                    ['adults' => 4, 'children' => 0], // 4 adults
                    ['adults' => 3, 'children' => 0], // 3 adults
                    ['adults' => 2, 'children' => 0], // 2 adults
                    ['adults' => 2, 'children' => 2], // 2 adults, 2 children
                    ['adults' => 2, 'children' => 3], // 2 adults, 3 children
                    ['adults' => 3, 'children' => 2], // 3 adults, 2 children
                    ['adults' => 3, 'children' => 1], // 3 adults, 1 child
                ]
            ],
        ];

        // Try to find a matching configuration for this room type
        $matchFound = false;
        $config = null;
        
        // Check for exact match first
        if (isset($roomConfigurations[$roomType])) {
            $config = $roomConfigurations[$roomType];
            $matchFound = true;
        } else {
            // If no exact match, try partial matching
            foreach (array_keys($roomConfigurations) as $configKey) {
                if (stripos($roomType, $configKey) !== false || 
                    stripos($configKey, $roomType) !== false) {
                    $config = $roomConfigurations[$configKey];
                    $matchFound = true;
                    break;
                }
            }
        }

        // If room type is not in our configurations, use basic validation
        if (!$matchFound) {
            \Illuminate\Support\Facades\Log::info('Room model: No configuration found for room type, using basic validation', [
                'roomType' => $roomType
            ]);
            return true;
        }

        $totalOccupancy = $adults + $children;

        // For Standard Room, only allow specific combinations
        if (stripos($roomType, 'standard') !== false) {
            // Check if at least one adult (we need an adult for booking)
            if ($adults < 1) {
                return false;
            }

            // Check if total exceeds max_total
            if ($totalOccupancy > $config['max_total']) {
                \Illuminate\Support\Facades\Log::info('Room model: REJECTED - Standard Room total occupancy exceeds maximum', [
                    'roomType' => $roomType,
                    'total' => $totalOccupancy,
                    'max_total' => $config['max_total']
                ]);
                return false;
            }

            // Check if this combination is allowed
            foreach ($config['allowed_combinations'] as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    \Illuminate\Support\Facades\Log::info('Room model: ACCEPTED - Standard Room valid combination', [
                        'roomType' => $roomType,
                        'adults' => $adults,
                        'children' => $children
                    ]);
                    return true;
                }
            }

            // If no exact match found, return false (strict validation)
            \Illuminate\Support\Facades\Log::info('Room model: REJECTED - Standard Room combination not allowed', [
                'roomType' => $roomType,
                'adults' => $adults,
                'children' => $children
            ]);
            return false;
        }

        // For Double Bed Room, only allow specific combinations
        if (stripos($roomType, 'double') !== false) {
            // Check if this combination is allowed
            foreach ($config['allowed_combinations'] as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    return true;
                }
            }

            // If no exact match found, return false (strict validation)
            return false;
        }

        // For other room types, check total capacity
        if ($totalOccupancy > $config['max_total']) {
            return false;
        }

        // Check if this combination is allowed
        foreach ($config['allowed_combinations'] as $combination) {
            if ($combination['adults'] === $adults && $combination['children'] === $children) {
                return true;
            }
        }

        // If no exact match found, but total is within max_total, allow it
        // This makes the system more flexible for different combinations
        return true;
    }

    /**
     * Get room types that match the given adults and children count
     */
    public static function getMatchingRoomTypes(int $adults, int $children): array
    {
        // Log the request for debugging
        \Illuminate\Support\Facades\Log::info('Room::getMatchingRoomTypes called', [
            'adults' => $adults,
            'children' => $children
        ]);

        $matchingTypes = [];
        $totalOccupancy = $adults + $children;

        // Special case: Standard Room with 2 adults and 2 children is not allowed
        $excludeStandardRoom = ($adults === 2 && $children === 2);
        
        if ($excludeStandardRoom) {
            \Illuminate\Support\Facades\Log::info('Room::getMatchingRoomTypes - Excluding Standard Room for 2 adults and 2 children');
        }

        // For Standard Room, only allow specific combinations
        $standardRoomCombinations = [
            ['adults' => 1, 'children' => 2], // 1 adult, 2 children
            ['adults' => 2, 'children' => 1], // 2 adults, 1 child
            ['adults' => 1, 'children' => 1], // 1 adult, 1 child
            ['adults' => 1, 'children' => 0], // 1 adult, no children
            ['adults' => 2, 'children' => 0], // 2 adults, no children
            ['adults' => 3, 'children' => 0], // 3 adults, no children
            // Note: 2 adults, 2 children is explicitly excluded
        ];

        // Skip Standard Room check if we're excluding it
        if (!$excludeStandardRoom) {
            foreach ($standardRoomCombinations as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    $matchingTypes[] = 'Standard Room';
                    \Illuminate\Support\Facades\Log::info('Standard Room matched', [
                        'adults' => $adults,
                        'children' => $children
                    ]);
                    break;
                }
            }
        }

        // For other room types, use the flexible configuration
        $roomConfigurations = [
            'Double Bed Room' => [
                'max_total' => 4,
                'allowed_combinations' => [
                    ['adults' => 3, 'children' => 1], // 3 adults, 1 child
                    ['adults' => 3, 'children' => 0], // 3 adults, no children
                    ['adults' => 2, 'children' => 2], // 2 adults, 2 children
                    ['adults' => 2, 'children' => 1], // 2 adults, 1 child
                    ['adults' => 2, 'children' => 0], // 2 adults, no children
                    ['adults' => 1, 'children' => 3], // 1 adult, 3 children
                    ['adults' => 1, 'children' => 2], // 1 adult, 2 children
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child
                    ['adults' => 1, 'children' => 0], // 1 adult, no children
                    // Note: 4 adults is explicitly excluded
                ]
            ],
            'Luxury Room' => [
                'max_total' => 2,
                'allowed_combinations' => [
                    ['adults' => 2, 'children' => 0], // Only couples allowed
                    ['adults' => 1, 'children' => 1], // 1 adult, 1 child
                    ['adults' => 1, 'children' => 0], // 1 adult, no children (ADDED)
                ]
            ],
        ];

        foreach ($roomConfigurations as $roomType => $config) {
            // For Double Bed Room, only allow specific combinations
            if ($roomType === 'Double Bed Room') {
                // Check if this combination is allowed
                $isAllowed = false;
                foreach ($config['allowed_combinations'] as $combination) {
                    if ($combination['adults'] === $adults && $combination['children'] === $children) {
                        $isAllowed = true;
                        break;
                    }
                }

                if ($isAllowed) {
                    $matchingTypes[] = $roomType;
                }

                continue;
            }

            // For other room types
            // Check total capacity
            if (($adults + $children) > $config['max_total']) {
                continue;
            }

            // Check if this combination is allowed
            $isAllowed = false;
            foreach ($config['allowed_combinations'] as $combination) {
                if ($combination['adults'] === $adults && $combination['children'] === $children) {
                    $isAllowed = true;
                    break;
                }
            }

            // If no exact match found, but total is within max_total, allow it
            // This makes the system more flexible for different combinations
            if ($isAllowed || ($adults + $children) <= $config['max_total']) {
                $matchingTypes[] = $roomType;
            }
        }

        return $matchingTypes;
    }

    protected static function booted(): void
    {
        static::deleted(function (Room $room) {
            $room->amenities()->detach();
            $room->activeRoomDates()->delete();
            $room->inventory()->delete();
        });
    }
}
