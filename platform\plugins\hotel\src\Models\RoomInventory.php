<?php

namespace Botble\Hotel\Models;

use Botble\Base\Models\BaseModel;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class RoomInventory extends BaseModel
{
    protected $table = 'ht_room_inventory';

    protected $fillable = [
        'room_id',
        'room_id_code',
        'is_available',
    ];

    protected $casts = [
        'is_available' => 'boolean',
    ];

    public function room(): BelongsTo
    {
        return $this->belongsTo(Room::class, 'room_id');
    }

    /**
     * Check if this room is available for the given date range
     */
    public function isAvailableForDateRange($startDate, $endDate): bool
    {
        try {
            \Log::info('RoomInventory: Checking availability for date range', [
                'room_id_code' => $this->room_id_code,
                'start_date' => $startDate,
                'end_date' => $endDate,
                'is_available_flag' => $this->is_available
            ]);

            // Convert dates to Carbon instances for consistent handling
            $startDateCarbon = \Carbon\Carbon::parse($startDate);
            $endDateCarbon = \Carbon\Carbon::parse($endDate);

            // Check if there are any active bookings for this room that overlap with the requested dates
            $overlappingBookings = BookingRoom::query()
                ->join('ht_bookings', 'ht_bookings.id', '=', 'ht_booking_rooms.booking_id')
                ->where('ht_booking_rooms.room_id_code', $this->room_id_code)
                ->whereNotIn('ht_bookings.status', ['cancelled', 'rejected'])
                ->where(function ($query) use ($startDateCarbon, $endDateCarbon) {
                    $query->where(function ($q) use ($startDateCarbon, $endDateCarbon) {
                        // Case 1: Existing booking starts within our requested range
                        $q->whereDate('ht_booking_rooms.start_date', '>=', $startDateCarbon->format('Y-m-d'))
                          ->whereDate('ht_booking_rooms.start_date', '<', $endDateCarbon->format('Y-m-d'));
                    })->orWhere(function ($q) use ($startDateCarbon, $endDateCarbon) {
                        // Case 2: Existing booking ends within our requested range
                        $q->whereDate('ht_booking_rooms.end_date', '>', $startDateCarbon->format('Y-m-d'))
                          ->whereDate('ht_booking_rooms.end_date', '<=', $endDateCarbon->format('Y-m-d'));
                    })->orWhere(function ($q) use ($startDateCarbon, $endDateCarbon) {
                        // Case 3: Existing booking completely spans our requested range
                        $q->whereDate('ht_booking_rooms.start_date', '<=', $startDateCarbon->format('Y-m-d'))
                          ->whereDate('ht_booking_rooms.end_date', '>=', $endDateCarbon->format('Y-m-d'));
                    });
                })
                ->count();

            if ($overlappingBookings > 0) {
                \Log::info('RoomInventory: Room unavailable due to overlapping bookings', [
                    'room_id_code' => $this->room_id_code,
                    'overlapping_bookings' => $overlappingBookings
                ]);
                return false;
            }

            // Also check the room_dates table for bookings if it exists and has the required columns
            try {
                if (\Schema::hasTable('ht_room_dates') &&
                    \Schema::hasColumn('ht_room_dates', 'is_booked') &&
                    \Schema::hasColumn('ht_room_dates', 'room_id_code')) {

                    $roomDateBookings = \DB::table('ht_room_dates')
                        ->where('room_id', $this->room_id)
                        ->where('room_id_code', $this->room_id_code)
                        ->where('is_booked', true)
                        ->where(function ($query) use ($startDateCarbon, $endDateCarbon) {
                            $query->where(function ($q) use ($startDateCarbon, $endDateCarbon) {
                                // Case 1: Booking starts within our range
                                $q->whereDate('start_date', '>=', $startDateCarbon->format('Y-m-d'))
                                  ->whereDate('start_date', '<', $endDateCarbon->format('Y-m-d'));
                            })->orWhere(function ($q) use ($startDateCarbon, $endDateCarbon) {
                                // Case 2: Booking ends within our range
                                $q->whereDate('end_date', '>', $startDateCarbon->format('Y-m-d'))
                                  ->whereDate('end_date', '<=', $endDateCarbon->format('Y-m-d'));
                            })->orWhere(function ($q) use ($startDateCarbon, $endDateCarbon) {
                                // Case 3: Booking spans our entire range
                                $q->whereDate('start_date', '<=', $startDateCarbon->format('Y-m-d'))
                                  ->whereDate('end_date', '>=', $endDateCarbon->format('Y-m-d'));
                            });
                        })
                        ->count();

                    if ($roomDateBookings > 0) {
                        \Log::info('RoomInventory: Room unavailable due to room_dates bookings', [
                            'room_id_code' => $this->room_id_code,
                            'room_date_bookings' => $roomDateBookings
                        ]);
                        return false;
                    }
                }
            } catch (\Exception $e) {
                \Log::warning('RoomInventory: Error checking room_dates table', [
                    'room_id_code' => $this->room_id_code,
                    'error' => $e->getMessage()
                ]);
                // Continue with availability check even if room_dates check fails
            }

            // If we reach here, no overlapping bookings were found
            $isAvailable = true;

            \Log::info('RoomInventory: Room availability check completed', [
                'room_id_code' => $this->room_id_code,
                'is_available' => $isAvailable
            ]);

            return $isAvailable;

        } catch (\Exception $e) {
            \Log::error('RoomInventory: Error checking room availability', [
                'room_id_code' => $this->room_id_code,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            // If there's an error, return true to avoid blocking bookings unnecessarily
            // This is a conservative approach that favors availability over strict blocking
            return true;
        }
    }
}
