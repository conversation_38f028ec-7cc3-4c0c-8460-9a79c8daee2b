// Find the validation rule for room_id
protected function validateRoom(Request $request)
{
    // Check if we should bypass room validation
    if ($request->has('bypass_room_validation') || 
        in_array($request->input('status'), ['completed', 'cancelled'])) {
        return $request->validate([
            'room_id' => 'nullable', // Make room_id optional for completed/cancelled
            // Other validation rules...
        ]);
    }
    
    return $request->validate([
        'room_id' => 'required|exists:rooms,id',
        // Other validation rules...
    ]);
}

// Update the store method to handle both input types
public function store(Request $request)
{
    // Check if we should bypass room validation
    if ($request->has('bypass_room_validation') || 
        in_array($request->input('status'), ['completed', 'cancelled'])) {
        // Process the booking without requiring room_id
        // ...
        return redirect()->route('bookings.index')->with('success', 'Booking created successfully.');
    }
    
    // Check if room_id is provided either by direct input or selection
    $roomId = $request->input('room_id') ?: $request->input('room_id_code');
    
    if (!empty($roomId) && $roomId !== 'Select Room ID') {
        // Process the room booking
        // ...
        return redirect()->route('bookings.index')->with('success', 'Booking created successfully.');
    } else {
        return back()->withErrors(['room_id' => 'Please select a valid room ID.']);
    }
}

