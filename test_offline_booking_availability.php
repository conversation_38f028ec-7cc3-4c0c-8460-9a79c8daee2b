<?php

/**
 * Test script for debugging offline booking room availability issues
 * 
 * This script helps identify problems with the room availability filter
 * by testing various scenarios and providing detailed logging.
 */

require_once __DIR__ . '/vendor/autoload.php';

use Illuminate\Support\Facades\Log;
use Botble\Hotel\Models\Room;
use Botble\Hotel\Models\RoomInventory;
use Botble\Hotel\Models\BookingRoom;
use Carbon\Carbon;

class OfflineBookingAvailabilityTester
{
    public function testRoomAvailability()
    {
        echo "=== Offline Booking Room Availability Test ===\n\n";

        // Test parameters
        $testCases = [
            [
                'start_date' => Carbon::today()->format('Y-m-d'),
                'end_date' => Carbon::today()->addDays(2)->format('Y-m-d'),
                'adults' => 1,
                'children' => 0,
                'number_of_rooms' => 1,
                'description' => 'Today to +2 days, 1 adult'
            ],
            [
                'start_date' => Carbon::today()->addDays(7)->format('Y-m-d'),
                'end_date' => Carbon::today()->addDays(9)->format('Y-m-d'),
                'adults' => 2,
                'children' => 0,
                'number_of_rooms' => 1,
                'description' => '+7 to +9 days, 2 adults'
            ],
            [
                'start_date' => Carbon::today()->addDays(14)->format('Y-m-d'),
                'end_date' => Carbon::today()->addDays(16)->format('Y-m-d'),
                'adults' => 1,
                'children' => 1,
                'number_of_rooms' => 2,
                'description' => '+14 to +16 days, 1 adult + 1 child, 2 rooms'
            ]
        ];

        foreach ($testCases as $index => $testCase) {
            echo "--- Test Case " . ($index + 1) . ": {$testCase['description']} ---\n";
            $this->runSingleTest($testCase);
            echo "\n";
        }

        echo "=== Test Summary ===\n";
        $this->generateSummaryReport();
    }

    protected function runSingleTest($testCase)
    {
        $startDate = $testCase['start_date'];
        $endDate = $testCase['end_date'];
        $adults = $testCase['adults'];
        $children = $testCase['children'];
        $numberOfRooms = $testCase['number_of_rooms'];

        echo "Parameters:\n";
        echo "  Start Date: {$startDate}\n";
        echo "  End Date: {$endDate}\n";
        echo "  Adults: {$adults}\n";
        echo "  Children: {$children}\n";
        echo "  Number of Rooms: {$numberOfRooms}\n\n";

        try {
            // Get all published rooms
            $allRooms = Room::with(['category', 'currency', 'inventory'])
                ->where('status', 'published')
                ->get();

            echo "Total published rooms: " . $allRooms->count() . "\n";

            $availableRooms = [];
            $unavailableRooms = [];

            foreach ($allRooms as $room) {
                echo "\nChecking Room: {$room->name} (ID: {$room->id})\n";

                // Check occupancy validation
                $isValidOccupancy = $room->isValidCombination($room->name, $adults, $children);
                echo "  Occupancy Valid: " . ($isValidOccupancy ? 'YES' : 'NO') . "\n";

                if (!$isValidOccupancy) {
                    $unavailableRooms[] = [
                        'room' => $room,
                        'reason' => 'Invalid occupancy combination'
                    ];
                    continue;
                }

                // Check availability
                $isAvailable = $this->checkRoomAvailabilityDetailed($room, $startDate, $endDate, 1);
                echo "  Available: " . ($isAvailable ? 'YES' : 'NO') . "\n";

                if ($isAvailable) {
                    $availableRooms[] = $room;
                } else {
                    $unavailableRooms[] = [
                        'room' => $room,
                        'reason' => 'Not available for selected dates'
                    ];
                }
            }

            echo "\n--- Results ---\n";
            echo "Available Rooms: " . count($availableRooms) . "\n";
            foreach ($availableRooms as $room) {
                echo "  - {$room->name} ({$room->category->name ?? 'No category'})\n";
            }

            echo "\nUnavailable Rooms: " . count($unavailableRooms) . "\n";
            foreach ($unavailableRooms as $item) {
                echo "  - {$item['room']->name}: {$item['reason']}\n";
            }

        } catch (\Exception $e) {
            echo "ERROR: " . $e->getMessage() . "\n";
            echo "Trace: " . $e->getTraceAsString() . "\n";
        }
    }

    protected function checkRoomAvailabilityDetailed($room, $startDate, $endDate, $requestedRooms = 1)
    {
        echo "    Checking availability details...\n";

        try {
            // Check if room has inventory
            $inventory = $room->inventory;
            if ($inventory && $inventory->isNotEmpty()) {
                echo "    Using inventory-based check (inventory count: " . $inventory->count() . ")\n";

                $availableInventoryCount = 0;
                foreach ($inventory as $inventoryItem) {
                    $itemAvailable = $inventoryItem->isAvailableForDateRange($startDate, $endDate);
                    echo "      Inventory {$inventoryItem->room_id_code}: " . ($itemAvailable ? 'Available' : 'Unavailable') . "\n";
                    if ($itemAvailable) {
                        $availableInventoryCount++;
                    }
                }

                $isAvailable = $availableInventoryCount >= $requestedRooms;
                echo "    Available inventory: {$availableInventoryCount}, Requested: {$requestedRooms}\n";
                return $isAvailable;
            }

            // Use legacy availability check
            echo "    Using legacy availability check\n";
            $isAvailable = $room->isAvailableAt([
                'start_date' => $startDate,
                'end_date' => $endDate,
                'rooms' => $requestedRooms,
            ]);

            return $isAvailable;

        } catch (\Exception $e) {
            echo "    ERROR in availability check: " . $e->getMessage() . "\n";
            return false;
        }
    }

    protected function generateSummaryReport()
    {
        try {
            // Get database statistics
            $totalRooms = Room::where('status', 'published')->count();
            $roomsWithInventory = Room::whereHas('inventory')->count();
            $totalInventoryItems = RoomInventory::count();
            $availableInventoryItems = RoomInventory::where('is_available', true)->count();
            $totalBookings = BookingRoom::count();
            $activeBookings = BookingRoom::whereHas('booking', function($query) {
                $query->whereNotIn('status', ['cancelled', 'rejected']);
            })->count();

            echo "Database Statistics:\n";
            echo "  Total Published Rooms: {$totalRooms}\n";
            echo "  Rooms with Inventory: {$roomsWithInventory}\n";
            echo "  Total Inventory Items: {$totalInventoryItems}\n";
            echo "  Available Inventory Items: {$availableInventoryItems}\n";
            echo "  Total Bookings: {$totalBookings}\n";
            echo "  Active Bookings: {$activeBookings}\n\n";

            // Check for common issues
            echo "Potential Issues:\n";
            
            if ($roomsWithInventory == 0) {
                echo "  ⚠️  No rooms have inventory items - using legacy availability logic\n";
            }
            
            if ($availableInventoryItems == 0 && $totalInventoryItems > 0) {
                echo "  ⚠️  All inventory items are marked as unavailable\n";
            }
            
            if ($totalRooms == 0) {
                echo "  ❌ No published rooms found\n";
            }

            echo "\nRecommendations:\n";
            echo "  1. Check room inventory setup if using inventory-based availability\n";
            echo "  2. Verify room occupancy rules in Room model\n";
            echo "  3. Check for overlapping bookings in date ranges\n";
            echo "  4. Review room status (should be 'published')\n";

        } catch (\Exception $e) {
            echo "Error generating summary: " . $e->getMessage() . "\n";
        }
    }
}

// Run the test if this script is executed directly
if (php_sapi_name() === 'cli') {
    echo "Starting Offline Booking Availability Test...\n\n";
    
    try {
        // Initialize Laravel application
        $app = require_once __DIR__ . '/bootstrap/app.php';
        $app->make('Illuminate\Contracts\Console\Kernel')->bootstrap();
        
        $tester = new OfflineBookingAvailabilityTester();
        $tester->testRoomAvailability();
        
    } catch (\Exception $e) {
        echo "Failed to initialize application: " . $e->getMessage() . "\n";
        echo "Make sure to run this script from the Laravel root directory.\n";
    }
}
